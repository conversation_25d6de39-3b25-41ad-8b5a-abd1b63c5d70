{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FXRP", "AssetManager_FDOGE", "AssetManager_FBTC"], "facets": [{"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "ChallengesFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralReservationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "LiquidationFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionHandshakeFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "UnderlyingBalanceFacet", "exposedInterfaces": ["IIAssetManager"]}]}