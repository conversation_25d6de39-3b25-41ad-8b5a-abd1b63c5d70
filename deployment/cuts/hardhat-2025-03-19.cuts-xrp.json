{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FXRP"], "facets": [{"contract": "AgentAlwaysAllowedMintersFacet", "exposedInterfaces": ["IAgentAlwaysAllowedMinters"]}, {"contract": "CoreVaultFacet", "exposedInterfaces": ["ICoreVault"]}, {"contract": "CoreVaultSettingsFacet", "exposedInterfaces": ["ICoreVaultSettings"]}, {"contract": "AgentVaultManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentSettingsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralReservationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionConfirmationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionDefaultsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionHandshakeFacet", "exposedInterfaces": ["IIAssetManager"]}], "init": {"contract": "CoreVaultSettingsFacet", "method": "initCoreVaultFacet", "args": ["******************************************", "******************************************", 0, 2000, 0, 10]}}