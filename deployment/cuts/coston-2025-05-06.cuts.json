{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FTestBTC", "AssetManager_FTestDOGE", "AssetManager_FTestXRP", "AssetManager_FSimCoinX"], "facets": [{"contract": "AgentAlwaysAllowedMintersFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentCollateralFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentInfoFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentSettingsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentVaultManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AvailableAgentsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "ChallengesFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralReservationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "LiquidationFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionConfirmationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionDefaultsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionHandshakeFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "SettingsManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "SettingsReaderFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "SystemInfoFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "UnderlyingBalanceFacet", "exposedInterfaces": ["IIAssetManager"]}]}