{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FTestXRP", "AssetManager_FSimCoinX"], "facets": [{"contract": "RedemptionTimeExtensionFacet", "exposedInterfaces": ["IRedemptionTimeExtension"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}], "init": {"contract": "RedemptionTimeExtensionFacet", "method": "initRedemptionTimeExtensionFacet", "args": [15]}}