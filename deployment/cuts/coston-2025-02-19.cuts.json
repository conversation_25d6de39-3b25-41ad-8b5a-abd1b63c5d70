{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FTestBTC", "AssetManager_FTestDOGE", "AssetManager_FTestXRP"], "facets": [{"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionConfirmationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionDefaultsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionHandshakeFacet", "exposedInterfaces": ["IIAssetManager"]}]}