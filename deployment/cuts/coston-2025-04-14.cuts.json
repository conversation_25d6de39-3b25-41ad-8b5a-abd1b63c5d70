{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FTestBTC", "AssetManager_FTestDOGE", "AssetManager_FTestXRP", "AssetManager_FSimCoinX"], "facets": [{"contract": "SettingsReaderFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentVaultManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentSettingsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentInfoFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentCollateralFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralReservationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionConfirmationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionDefaultsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionHandshakeFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AvailableAgentsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "LiquidationFacet", "exposedInterfaces": ["IIAssetManager"]}]}