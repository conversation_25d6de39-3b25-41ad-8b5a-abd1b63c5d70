[{"name": "InflationAllocation", "contractName": "InflationAllocation.sol", "address": "0xD11CB940C8375c119CAe7b8A75844ec35371F2E0"}, {"name": "<PERSON><PERSON>", "contractName": "Relay.sol", "address": "0x92a6E1127262106611e1e129BB64B6D8654273F7"}, {"name": "FdcHub", "contractName": "FdcHub.sol", "address": "0x1c78A073E3BD2aCa4cc327d55FB0cD4f0549B55b"}, {"name": "FlareDaemon", "contractName": "FlareDaemon.sol", "address": "0x1000000000000000000000000000000000000002"}, {"name": "PriceSubmitter", "contractName": "PriceSubmitter.sol", "address": "0x1000000000000000000000000000000000000003"}, {"name": "Inflation", "contractName": "Inflation.sol", "address": "0x12d20f1b378cd8ee8dA4729262645EC62FD52307"}, {"name": "Supply", "contractName": "Supply.sol", "address": "0x98D2B1835A8D4a7c9915Ca212bDb5AA259642fac"}, {"name": "FtsoRewardManager", "contractName": "FtsoRewardManager.sol", "address": "0xfD36176C63dA52E783a347DE3544B0b44C7054a6"}, {"name": "CleanupBlockNumberManager", "contractName": "CleanupBlockNumberManager.sol", "address": "0xB50DAcdaA3Af02F8A0533902C117ADFC31A31Ccf"}, {"name": "FtsoRegistry", "contractName": "FtsoRegistry.sol", "address": "0xf7Bbf40145C82Fca13011C783AaeCa6bD95fd652"}, {"name": "VoterWhitelister", "contractName": "VoterWhitelister.sol", "address": "0xFAe0fd738dAbc8a0426F47437322b6d026A9FD95"}, {"name": "FtsoManager", "contractName": "FtsoManager.sol", "address": "0x12B6E9dB4Ac9889aBb92beAA6CF7d71f334c1168"}, {"name": "WNat", "contractName": "WNat.sol", "address": "0x767b25A658E8FC8ab6eBbd52043495dB61b4ea91"}, {"name": "WCFLR", "contractName": "WNat.sol", "address": "0x767b25A658E8FC8ab6eBbd52043495dB61b4ea91"}, {"name": "FtsoWnat", "contractName": "Ftso.sol", "address": "0xaD256562ed63ABD503935CbEE276a72160d200e8"}, {"name": "FtsoTestXrp", "contractName": "Ftso.sol", "address": "0x655bC84c44447d8A6087300dEd38eEcc125721B8"}, {"name": "FtsoTestLtc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestXlm", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestDoge", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAda", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAlgo", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestBtc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestEth", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestFil", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestArb", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAvax", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestBnb", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestMatic", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestSol", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestUsdc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestUsdt", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestXdc", "contractName": "Ftso.sol", "address": "0x109007f065EcEF4994bCB67a1891764F949088fe"}, {"name": "GovernanceSettings", "contractName": "GovernanceSettings.sol", "address": "0x183fAfF6997C98A812A3B98748Fc80241D08f312"}, {"name": "GovernanceVotePower", "contractName": "GovernanceVotePower.sol", "address": "0xEb10E3a19Ae0e2564E2c86dBd89517fe7F24789d"}, {"name": "PollingFoundation", "contractName": "PollingFoundation.sol", "address": "0xe435D00D30DEc987Afd5dAC2205F2b20e2F90eB8"}, {"name": "AddressUpdater", "contractName": "AddressUpdater.sol", "address": "0xb006c0cb0b2b8ae48E5B12883Cd5a13654cc6e20"}, {"name": "FlareContractRegistry", "contractName": "FlareContractRegistry.sol", "address": "0xaD67FE66660Fb8dFE9d6b1b4240d8650e30F6019"}, {"name": "PollingFtso", "contractName": "PollingFtso.sol", "address": "0xD036a8F254ef782cb93af4F829A1568E992c3864"}, {"name": "ClaimSetupManager", "contractName": "ClaimSetupManager.sol", "address": "0x3F55A3c8012C26B140AcC23d061E7c4626d64e01"}, {"name": "DelegationAccount", "contractName": "DelegationAccount.sol", "address": "0xE2Ee339b2608Fe20ca2f721e938a640e0E5ee546"}, {"name": "Flare<PERSON>set<PERSON>ry", "contractName": "FlareAssetRegistry.sol", "address": "******************************************"}, {"name": "WNatRegistryProvider", "contractName": "WNatRegistryProvider.sol", "address": "******************************************"}, {"name": "testUSDC", "contractName": "FakeERC20.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "testUSDT", "contractName": "FakeERC20.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "testETH", "contractName": "FakeERC20.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FdcVerification", "contractName": "FdcVerification.sol", "address": "******************************************"}, {"name": "Price<PERSON><PERSON><PERSON>", "contractName": "FtsoV2PriceStore.sol", "address": "******************************************"}, {"name": "FtsoV2PriceStore", "contractName": "FtsoV2PriceStore.sol", "address": "******************************************"}, {"name": "FakePriceReader", "contractName": "FakePriceReader.sol", "address": "******************************************"}, {"name": "AgentOwnerRegistry", "contractName": "AgentOwnerRegistry.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contractName": "Whitelist.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "AgentVaultProxyImplementation", "contractName": "AgentVault.sol", "address": "0x299d678f67e7ADD4efdf295Ebe0E92FCb4f75C4c"}, {"name": "CollateralPoolProxyImplementation", "contractName": "CollateralPool.sol", "address": "0x6351C67FC18dEDB3A89EC84762d4e84AeBa30666"}, {"name": "CollateralPoolTokenProxyImplementation", "contractName": "CollateralPoolToken.sol", "address": "0xFeA3Bf40B5a75fad7b4e2CBEcEE2Bd4eE899698e"}, {"name": "AgentVaultFactory", "contractName": "AgentVaultFactory.sol", "address": "0x1a6d9C80a2BCD949C6Fb87E9c2a98342970c3c0D"}, {"name": "CollateralPoolFactory", "contractName": "CollateralPoolFactory.sol", "address": "0x795e38AF5175e4728FE9719220634C8134568224"}, {"name": "CollateralPoolTokenFactory", "contractName": "CollateralPoolTokenFactory.sol", "address": "0xAF7EB55cC12651e6681d08b9df31aE1dD0C9e857"}, {"name": "AssetManagerControllerImplementation", "contractName": "AssetManagerController.sol", "address": "0xA36b57bF846Ce819eE260dC25176e6f893Dc7798"}, {"name": "AssetManagerController", "contractName": "AssetManagerControllerProxy.sol", "address": "0x572DeF121DC83332887E25e34aD51C2f5f40BC97", "mustSwitchToProduction": true}, {"name": "FAssetImplementation", "contractName": "FAsset.sol", "address": "0xC90cb1C783A4bd1600A4fBCBD92484FeE170a64B"}, {"name": "AssetManager_FTestXRP", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FTestXRP", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FSimCoinX", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FSimCoinX", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FTestBTC", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FTestBTC", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FTestDOGE", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FTestDOGE", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManagerInit", "contractName": "AssetManagerInit.sol", "address": "******************************************"}, {"name": "AssetManagerDiamondCutFacet", "contractName": "AssetManagerDiamondCutFacet.sol", "address": "******************************************"}, {"name": "DiamondLoupeFacet", "contractName": "DiamondLoupeFacet.sol", "address": "******************************************"}, {"name": "AgentInfoFacet", "contractName": "AgentInfoFacet.sol", "address": "******************************************"}, {"name": "AvailableAgentsFacet", "contractName": "AvailableAgentsFacet.sol", "address": "0x47C8fEd6b3aB257720554A655422f31e9DaB5B0b"}, {"name": "CollateralReservationsFacet", "contractName": "CollateralReservationsFacet.sol", "address": "0x939e8dCaC11d450e29F42A69B30774347fe62Ff5"}, {"name": "MintingFacet", "contractName": "MintingFacet.sol", "address": "0x1330CABE5E3597d1aA3204D3C1c97484e845356E"}, {"name": "RedemptionRequestsFacet", "contractName": "RedemptionRequestsFacet.sol", "address": "0x8194BDde55485a8b85EF8A3AF874D977Ee9dbdFd"}, {"name": "RedemptionConfirmationsFacet", "contractName": "RedemptionConfirmationsFacet.sol", "address": "0x13eaeF1EdbC000247704147CF160941b2B4eBF60"}, {"name": "RedemptionDefaultsFacet", "contractName": "RedemptionDefaultsFacet.sol", "address": "0x27454751E8e39D1330fa5056D115007EEC7bFa73"}, {"name": "RedemptionHandshakeFacet", "contractName": "RedemptionHandshakeFacet.sol", "address": "0xd0C5B1FCE1EA1a8253a625dFB9Bf1646a7b6C1Cd"}, {"name": "LiquidationFacet", "contractName": "LiquidationFacet.sol", "address": "0x5D26E43F711b0Db582efd8B79639663f209f4357"}, {"name": "ChallengesFacet", "contractName": "ChallengesFacet.sol", "address": "0xA359EBD6C55953beeaeE9a50De49D67372Cd9f83"}, {"name": "UnderlyingBalanceFacet", "contractName": "UnderlyingBalanceFacet.sol", "address": "0x2DBd7C5B1b93Cb07D1719d5f733E394AEEAACBcc"}, {"name": "UnderlyingTimekeepingFacet", "contractName": "UnderlyingTimekeepingFacet.sol", "address": "0xFd0d363142C71E19a3F39288de056a4eb6D76fa4"}, {"name": "AgentVaultManagementFacet", "contractName": "AgentVaultManagementFacet.sol", "address": "0x3C661d203710274d5A2D9b27FD2730aE6F52BaF6"}, {"name": "AgentSettingsFacet", "contractName": "AgentSettingsFacet.sol", "address": "0x4DBcA5aa20988AfefBe9f873AFe20D0E69C06588"}, {"name": "CollateralTypesFacet", "contractName": "CollateralTypesFacet.sol", "address": "0xEe1218aCbE955836FB6fD4c8F1a24bae57C58690"}, {"name": "AgentCollateralFacet", "contractName": "AgentCollateralFacet.sol", "address": "0xFCB106b717cFF3c1f57c39cA81Ae676aD45F1C1f"}, {"name": "SettingsReaderFacet", "contractName": "SettingsReaderFacet.sol", "address": "0x8039Abf3A8F1204023FCea3837f5678f27CBb03f"}, {"name": "SettingsManagementFacet", "contractName": "SettingsManagementFacet.sol", "address": "0x8C2176E1cE9aBe4Df510ECebD067468AFE15D3EE"}, {"name": "AgentVaultAndPoolSupportFacet", "contractName": "AgentVaultAndPoolSupportFacet.sol", "address": "0xbA17AF59dCf544AaFE69F622eB93bFc0Ed1B1a6a"}, {"name": "SystemStateManagementFacet", "contractName": "SystemStateManagementFacet.sol", "address": "0xD5a79Ee4F36fdd0355a7AE87D2fc9cF591EAe117"}, {"name": "SystemInfoFacet", "contractName": "SystemInfoFacet.sol", "address": "0x17F6ee22ef6d66DE71F9303CF7351b74E8168720"}, {"name": "EmergencyPauseFacet", "contractName": "EmergencyPauseFacet.sol", "address": "0xDc658B7656F3c72BDA74a66B8c528e0494B358eB"}, {"name": "AgentPingFacet", "contractName": "AgentPingFacet.sol", "address": "0xa7a04172BeBecC59ee219a162f043d37F370c452"}, {"name": "RedemptionTimeExtensionFacet", "contractName": "RedemptionTimeExtensionFacet.sol", "address": "0x9A56c590Ea5dA91708085fE0C3De8DBeC22b58A7"}, {"name": "TransferFeeFacet", "contractName": "TransferFeeFacet.sol", "address": "0x0B15033cEaf41edbE1555fD2349e288593B9e5bC"}, {"name": "EmergencyPauseTransfersFacet", "contractName": "EmergencyPauseTransfersFacet.sol", "address": "0xc11CCc3705E9D6ec51119eCB9c21168426CAD5cf"}, {"name": "CoreVaultManagerImplementation", "contractName": "CoreVaultManager.sol", "address": "0xaC8E162224e73320eE83fA885Eccdf863eC7a532"}, {"name": "CoreVaultManager_FTestXRP", "contractName": "CoreVaultManagerProxy.sol", "address": "0xCC9206d850fb9140019E78e6b2B309193a34DA3c", "mustSwitchToProduction": true}, {"name": "AgentAlwaysAllowedMintersFacet", "contractName": "AgentAlwaysAllowedMintersFacet.sol", "address": "0x790Ca6E0085BFb7DDB4dF72dDd226F9c7e3B925d"}, {"name": "CoreVaultFacet", "contractName": "CoreVaultFacet.sol", "address": "0x9fE3Dd141e91FA0DF5EdcAE4Cf2bD14fF3d2F054"}, {"name": "CoreVaultSettingsFacet", "contractName": "CoreVaultSettingsFacet.sol", "address": "0xfC4eb60bC127008c3b499a1476BC24863ef3Be00"}]