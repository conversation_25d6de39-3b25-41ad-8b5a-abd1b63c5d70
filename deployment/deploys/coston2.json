[{"name": "<PERSON><PERSON>", "contractName": "Relay.sol", "address": "0x4087D4B5E009Af9FF41db910205439F82C3dc63c"}, {"name": "FdcHub", "contractName": "FdcHub.sol", "address": "TODO"}, {"name": "FlareDaemon", "contractName": "FlareDaemon.sol", "address": "0x1000000000000000000000000000000000000002"}, {"name": "PriceSubmitter", "contractName": "PriceSubmitter.sol", "address": "0x1000000000000000000000000000000000000003"}, {"name": "DistributionTreasury", "contractName": "DistributionTreasury.sol", "address": "0x062b351bdBF365B51dd34bC25c8eDe43100E4cbC"}, {"name": "IncentivePoolTreasury", "contractName": "IncentivePoolTreasury.sol", "address": "0x1000000000000000000000000000000000000005"}, {"name": "InitialAirdrop", "contractName": "InitialAirdrop.sol", "address": "0x8fB61aAbd150D2A236202Acc3F2CAFbf0aB41166"}, {"name": "GovernanceSettings", "contractName": "GovernanceSettings.sol", "address": "0x1000000000000000000000000000000000000007"}, {"name": "AddressUpdater", "contractName": "AddressUpdater.sol", "address": "0x707C840D053f28DF75223C151fA7aFa6d7eB3354"}, {"name": "InflationAllocation", "contractName": "InflationAllocation.sol", "address": "0xfa1903401A0cf7048A94C63D5dc5B4f36ff7b176"}, {"name": "Inflation", "contractName": "Inflation.sol", "address": "0x98B8e9B5830F04Fe3b8d56A2f8455E337037ba28"}, {"name": "Supply", "contractName": "Supply.sol", "address": "0x0496dcb45cDf498e3b797050f0469a0DE24F02e9"}, {"name": "FtsoRewardManager", "contractName": "FtsoRewardManager.sol", "address": "0x16E6A0b7a26518c336Bca0E935782C22A9EE07Fd"}, {"name": "CleanupBlockNumberManager", "contractName": "CleanupBlockNumberManager.sol", "address": "0xEaA8968E9B70dd2559066E3e801Df1596a4CFA23"}, {"name": "Escrow", "contractName": "Escrow.sol", "address": "0x05ad5ABCA6738f94C7b6CdFd9Da7E47F34966F01"}, {"name": "ValidatorRewardManager", "contractName": "ValidatorRewardManager.sol", "address": "0x33913AcE907F682E305f36d7538D3cCd37E2cA5B"}, {"name": "FtsoRegistry", "contractName": "FtsoRegistry.sol", "address": "0x48Da21ce34966A64E267CeFb78012C0282D0Ac87"}, {"name": "VoterWhitelister", "contractName": "VoterWhitelister.sol", "address": "0xa497f1Ed4edeA8151fECe457aa26e1D6A4318B6A"}, {"name": "DistributionToDelegators", "contractName": "DistributionToDelegators.sol", "address": "0xbd33bDFf04C357F7FC019E72D0504C24CF4Aa010"}, {"name": "IncentivePoolAllocation", "contractName": "IncentivePoolAllocation.sol", "address": "0xfaf3bDB0c455ef633A0B61E9424bcbC75FfaE29a"}, {"name": "IncentivePool", "contractName": "IncentivePool.sol", "address": "0x6892bDbBb14e1c9bD46Bf31E7BaC94d038fc82a6"}, {"name": "ValidatorRegistry", "contractName": "ValidatorRegistry.sol", "address": "0x4CDEBD06352B3eB6e3Eec31Be70aB8931De46Ffe"}, {"name": "FtsoManager", "contractName": "FtsoManager.sol", "address": "0x4F52e61907B0ED9f26b88F16B2510a4CA524d6d0"}, {"name": "WNat", "contractName": "WNat.sol", "address": "0xC67DCE33D7A8efA5FfEB961899C73fe01bCe9273"}, {"name": "WC2FLR", "contractName": "WNat.sol", "address": "0xC67DCE33D7A8efA5FfEB961899C73fe01bCe9273"}, {"name": "CombinedNat", "contractName": "CombinedNat.sol", "address": "0xa9ef58F5CFf9e282d759e36b249011729a74E7dd"}, {"name": "GovernanceVotePower", "contractName": "GovernanceVotePower.sol", "address": "0x8e4A2c063E1C82C9f5cb96489c0d2b6d78dF0538"}, {"name": "FtsoWnat", "contractName": "Ftso.sol", "address": "0xD2Ce99c898Df43F7774432BA57153ff79B9f2AFf"}, {"name": "FtsoTestXrp", "contractName": "Ftso.sol", "address": "0x9fFF391F0A1bb7393E3Abc67477d93cfcbA4F907"}, {"name": "FtsoTestLtc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestXlm", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestDoge", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAda", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAlgo", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestBtc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestEth", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestFil", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestArb", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAvax", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestBnb", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestMatic", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestSol", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestUsdc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestUsdt", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestXdc", "contractName": "Ftso.sol", "address": "0x017F6B006ef7B47191CB6b2D1C0444B028bA9952"}, {"name": "PollingFoundation", "contractName": "PollingFoundation.sol", "address": "0x48a59E8F8f29dA3dD1C2C2Fe54209f219535BFBA"}, {"name": "Flare<PERSON>set<PERSON>ry", "contractName": "FlareAssetRegistry.sol", "address": "0xC79E6Dc1817DddcB4206a0aDbb56832F476F4b67"}, {"name": "WNatRegistryProvider", "contractName": "WNatRegistryProvider.sol", "address": "0x8F438C5Fe273ac88085f5A7813b03cAfb3cAB129"}, {"name": "ClaimSetupManager", "contractName": "ClaimSetupManager.sol", "address": "0x5Ddb590530EF66775E6225671eaBD94959e9AE0e"}, {"name": "DelegationAccount", "contractName": "DelegationAccount.sol", "address": "0xA46ae6f0b180862236e5A309B1463EA02D3aD307"}, {"name": "FlareContractRegistry", "contractName": "FlareContractRegistry.sol", "address": "0xaD67FE66660Fb8dFE9d6b1b4240d8650e30F6019"}, {"name": "PollingFtso", "contractName": "PollingFtso.sol", "address": "0x501A498e8FDA589038d6526C2153a9fdc9d8eDD2"}, {"name": "AddressBinder", "contractName": "AddressBinder.sol", "address": "0x332aa9e37D64CAAF70ae5dBe8EFC2FC7611934Ae"}, {"name": "PChainStakeMirrorMultiSigVoting", "contractName": "PChainStakeMirrorMultiSigVoting.sol", "address": "0x37c5d6DCCb4421A47E3931584B306fBc00288aE9"}, {"name": "PChainStakeMirrorVerifier", "contractName": "PChainStakeMirrorVerifier.sol", "address": "******************************************"}, {"name": "<PERSON>hainStakeMirror", "contractName": "PChainStakeMirror.sol", "address": "******************************************"}, {"name": "testUSDC", "contractName": "FakeERC20.sol", "address": "******************************************"}, {"name": "testUSDT", "contractName": "FakeERC20.sol", "address": "******************************************"}, {"name": "testETH", "contractName": "FakeERC20.sol", "address": "******************************************"}]