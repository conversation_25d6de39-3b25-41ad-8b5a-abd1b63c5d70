[{"name": "GovernanceSettings", "contractName": "GovernanceSettings.sol", "address": "0x183fAfF6997C98A812A3B98748Fc80241D08f312"}, {"name": "AddressUpdater", "contractName": "AddressUpdater.sol", "address": "0x884E1818eA005171CDe0dB5b83E9734454e0Ed6D"}, {"name": "WNat", "contractName": "WNat.sol", "address": "0x02f0826ef6aD107Cfc861152B32B52fD11BaB9ED"}, {"name": "WSGB", "contractName": "WNat.sol", "address": "0x02f0826ef6aD107Cfc861152B32B52fD11BaB9ED"}, {"name": "<PERSON><PERSON>", "contractName": "Relay.sol", "address": "0x67a916E175a2aF01369294739AA60dDdE1Fad189"}, {"name": "FdcHub", "contractName": "FdcHub.sol", "address": "0xCfD4669a505A70c2cE85db8A1c1d14BcDE5a1a06"}, {"name": "FdcVerification", "contractName": "FdcVerification.sol", "address": "0xd283afC5A67E2d4Bc700b5B640328Bda22450621"}, {"name": "USDX", "contractName": "HexTrustUSD.sol", "address": "0x4A771Cc1a39FDd8AA08B8EA51F7Fd412e73B3d2B"}, {"name": "Price<PERSON><PERSON><PERSON>", "contractName": "FtsoV2PriceStore.sol", "address": "0x85973FA0098F5471e63C2c3FFa3ec8956eDEDF73"}, {"name": "FtsoV2PriceStore", "contractName": "FtsoV2PriceStore.sol", "address": "0x85973FA0098F5471e63C2c3FFa3ec8956eDEDF73"}, {"name": "AgentOwnerRegistry", "contractName": "AgentOwnerRegistry.sol", "address": "0xa7f5d3C81f55f2b072FB62a0D4A03317BFd1a3c0"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contractName": "Whitelist.sol", "address": "0xd5d90c61cF30B86b1c4507515e91470334d77Ad6"}, {"name": "AgentVaultProxyImplementation", "contractName": "AgentVault.sol", "address": "0x279615157757FecfA7B24Cc285A8970A12E68CCA"}, {"name": "AgentVaultFactory", "contractName": "AgentVaultFactory.sol", "address": "0x0f3B379f36A4854ae0ce52E363d5A3dc9c1CFee7"}, {"name": "CollateralPoolProxyImplementation", "contractName": "CollateralPool.sol", "address": "0xa9fF7ff60119D33e30814BbF91ba8613a3d4F8Ec"}, {"name": "CollateralPoolFactory", "contractName": "CollateralPoolFactory.sol", "address": "0x052C7Cb58FBd5964940D7a10cA78C50A90bAD3A7"}, {"name": "CollateralPoolTokenProxyImplementation", "contractName": "CollateralPoolToken.sol", "address": "0x8cde2269953B2f08102aE23ea86F2853e3446912"}, {"name": "CollateralPoolTokenFactory", "contractName": "CollateralPoolTokenFactory.sol", "address": "******************************************"}, {"name": "AssetManagerControllerImplementation", "contractName": "AssetManagerController.sol", "address": "******************************************"}, {"name": "AssetManagerController", "contractName": "AssetManagerControllerProxy.sol", "address": "******************************************"}, {"name": "FAssetImplementation", "contractName": "FAsset.sol", "address": "******************************************"}, {"name": "AssetManager_FBTC", "contractName": "AssetManager.sol", "address": "******************************************"}, {"name": "FBTC", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FDOGE", "contractName": "AssetManager.sol", "address": "******************************************"}, {"name": "FDOGE", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FXRP", "contractName": "AssetManager.sol", "address": "******************************************"}, {"name": "FXRP", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManagerInit", "contractName": "AssetManagerInit.sol", "address": "******************************************"}, {"name": "AssetManagerDiamondCutFacet", "contractName": "AssetManagerDiamondCutFacet.sol", "address": "******************************************"}, {"name": "DiamondLoupeFacet", "contractName": "DiamondLoupeFacet.sol", "address": "******************************************"}, {"name": "AgentInfoFacet", "contractName": "AgentInfoFacet.sol", "address": "0xA8e8f5620cBbA06fCbbB46D3eB222AFc25f3c698"}, {"name": "AvailableAgentsFacet", "contractName": "AvailableAgentsFacet.sol", "address": "0x3663775E34D691F590a3F1A83446353144229082"}, {"name": "CollateralReservationsFacet", "contractName": "CollateralReservationsFacet.sol", "address": "0x6B4f4032C9C739946F4862B2196F899b1045FA02"}, {"name": "MintingFacet", "contractName": "MintingFacet.sol", "address": "0xBd2279ec577148e228Efc888bf65674b7DAdED48"}, {"name": "RedemptionRequestsFacet", "contractName": "RedemptionRequestsFacet.sol", "address": "0x6fa01b66aA8bAE9A1A806642bea896fCC6c191d9"}, {"name": "RedemptionConfirmationsFacet", "contractName": "RedemptionConfirmationsFacet.sol", "address": "0x3E7575DA7eC4923c59945B8bBfBB3f2EEa6d3807"}, {"name": "RedemptionDefaultsFacet", "contractName": "RedemptionDefaultsFacet.sol", "address": "0x1a85cA9568BA9aC45A2254eeb137C372f6f1222d"}, {"name": "RedemptionHandshakeFacet", "contractName": "RedemptionHandshakeFacet.sol", "address": "0x5c93a5EA6EA9E412812cD48052A1bf7a6303bceD"}, {"name": "LiquidationFacet", "contractName": "LiquidationFacet.sol", "address": "0x66B2285698D7F16d0A6464799A4cD404dBf788B5"}, {"name": "ChallengesFacet", "contractName": "ChallengesFacet.sol", "address": "0xB2A618d52Ea70B53DAd347F920CDB8892feCb333"}, {"name": "UnderlyingBalanceFacet", "contractName": "UnderlyingBalanceFacet.sol", "address": "0xAc7BD3374984152B7838758574000E4CDC1B5655"}, {"name": "UnderlyingTimekeepingFacet", "contractName": "UnderlyingTimekeepingFacet.sol", "address": "0x8D3B8939978298264D3C6236121737ea143f64bC"}, {"name": "AgentVaultManagementFacet", "contractName": "AgentVaultManagementFacet.sol", "address": "0xf082799bc2e116dDa37Acb28a77A8302824343f3"}, {"name": "AgentSettingsFacet", "contractName": "AgentSettingsFacet.sol", "address": "0x0980195509385f9AAf30E42FdA12F5958439bF56"}, {"name": "CollateralTypesFacet", "contractName": "CollateralTypesFacet.sol", "address": "0xe5B1B6956B5A8F1F9389b5B2eD493E4cD5A8D1a5"}, {"name": "AgentCollateralFacet", "contractName": "AgentCollateralFacet.sol", "address": "0xE166FE64AdE428296052dBe6d1De32C7907D7D12"}, {"name": "SettingsReaderFacet", "contractName": "SettingsReaderFacet.sol", "address": "0x851B60b30c7Bd337f7d7a40335A58CA8ae58dd5d"}, {"name": "SettingsManagementFacet", "contractName": "SettingsManagementFacet.sol", "address": "0x21Bb15eC8Db6c24630eBB3A8e61b54a65825aff9"}, {"name": "AgentVaultAndPoolSupportFacet", "contractName": "AgentVaultAndPoolSupportFacet.sol", "address": "0x4A99CCBF7BF37b605aA014D5E54E7E00830E8f34"}, {"name": "SystemStateManagementFacet", "contractName": "SystemStateManagementFacet.sol", "address": "0xd0582c3F7f7Aa62db9B47F6b600A35E97fa77D28"}, {"name": "SystemInfoFacet", "contractName": "SystemInfoFacet.sol", "address": "0xF28437D72abF6ad8b6E8DDA87bAFEA6E9B7827A4"}, {"name": "EmergencyPauseFacet", "contractName": "EmergencyPauseFacet.sol", "address": "0xBdF02FE827596ee9B5f8fA3AcE9d497770E03FbE"}, {"name": "AgentPingFacet", "contractName": "AgentPingFacet.sol", "address": "0x36be8f2e1CC3339Cf6702CEfA69626271C36E2fd"}, {"name": "RedemptionTimeExtensionFacet", "contractName": "RedemptionTimeExtensionFacet.sol", "address": "0x32Cf6cF040631CE0C705325877F00Ee0eA252260"}, {"name": "TransferFeeFacet", "contractName": "TransferFeeFacet.sol", "address": "0xB9D6ee6d3e3863b38e570Ffe2338CA1788cc5eD3"}, {"name": "EmergencyPauseTransfersFacet", "contractName": "EmergencyPauseTransfersFacet.sol", "address": "0xf05338FeAB6c2416916110930D25bE0AC8D9257f"}, {"name": "AgentAlwaysAllowedMintersFacet", "contractName": "AgentAlwaysAllowedMintersFacet.sol", "address": "0x20b63a2377b6d3a0C61635bFEBa2E05844857dc2"}, {"name": "CoreVaultFacet", "contractName": "CoreVaultFacet.sol", "address": "0x86486058B0770289d1583A96CFE31cd9f9A4c2E7"}, {"name": "CoreVaultSettingsFacet", "contractName": "CoreVaultSettingsFacet.sol", "address": "0x4fBd9B0b588c4C2A998915cE493b5E69C1495f59"}, {"name": "CoreVaultManagerImplementation", "contractName": "CoreVaultManager.sol", "address": "0xE5b27233Baf6C8C8DA18f26DcBC9dC1c00Fc8321"}, {"name": "CoreVaultManager_FXRP", "contractName": "CoreVaultManagerProxy.sol", "address": "0x0CdF65f6de5FFFf9B39252d0296EcE2530770b5a"}]