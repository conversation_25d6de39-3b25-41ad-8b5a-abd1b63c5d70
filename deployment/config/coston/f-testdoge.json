{"$schema": "../asset-manager-parameters.schema.json", "burnAddress": "0x000000000000000000000000000000000000dEaD", "chainName": "testDOGE", "assetName": "Test DOGE", "assetSymbol": "testDOGE", "assetDecimals": 8, "fAssetName": "FDOGE", "fAssetSymbol": "FTestDOGE", "poolTokenSuffix": "TDOGE", "assetMintingDecimals": 8, "userWhitelist": null, "poolCollateral": {"token": "WNat", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "CFLR", "minCollateralRatioBIPS": 20000, "ccbMinCollateralRatioBIPS": 19000, "safetyMinCollateralRatioBIPS": 21000}, "vaultCollaterals": [{"token": "testUSDC", "decimals": 6, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "testUSDC", "minCollateralRatioBIPS": 14000, "ccbMinCollateralRatioBIPS": 13000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testUSDT", "decimals": 6, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "testUSDT", "minCollateralRatioBIPS": 14000, "ccbMinCollateralRatioBIPS": 13000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testETH", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "testETH", "minCollateralRatioBIPS": 14000, "ccbMinCollateralRatioBIPS": 13000, "safetyMinCollateralRatioBIPS": 15000}], "minUnderlyingBackingBIPS": 10000, "mintingCap": "0", "lotSize": "100 00000000", "requireEOAAddressProof": false, "collateralReservationFeeBIPS": 10, "mintingPoolHoldingsRequiredBIPS": 5000, "maxRedeemedTickets": 20, "redemptionFeeBIPS": 10, "redemptionDefaultFactorVaultCollateralBIPS": 11000, "redemptionDefaultFactorPoolBIPS": 0, "underlyingBlocksForPayment": 50, "underlyingSecondsForPayment": 3000, "averageBlockTimeMS": 60000, "attestationWindowSeconds": 86400, "confirmationByOthersAfterSeconds": 14400, "confirmationByOthersRewardUSD5": "100 00000", "paymentChallengeRewardBIPS": 0, "paymentChallengeRewardUSD5": "300 00000", "ccbTimeSeconds": 180, "liquidationStepSeconds": 180, "liquidationCollateralFactorBIPS": [10500, 11000, 11500], "liquidationFactorVaultCollateralBIPS": [10000, 10000, 10000], "maxTrustedPriceAgeSeconds": 600, "withdrawalWaitMinSeconds": 60, "announcedUnderlyingConfirmationMinSeconds": 250, "buybackCollateralFactorBIPS": 10030, "vaultCollateralBuyForFlareFactorBIPS": 10000, "minUpdateRepeatTimeSeconds": 60, "tokenInvalidationTimeMinSeconds": 86400, "agentExitAvailableTimelockSeconds": 60, "agentFeeChangeTimelockSeconds": 120, "agentMintingCRChangeTimelockSeconds": 120, "poolExitAndTopupChangeTimelockSeconds": 120, "agentTimelockedOperationWindowSeconds": 3600, "collateralPoolTokenTimelockSeconds": 60, "diamondCutMinTimelockSeconds": 7200, "maxEmergencyPauseDurationSeconds": 86400, "emergencyPauseDurationResetAfterSeconds": 604800, "redemptionPaymentExtensionSeconds": 60, "cancelCollateralReservationAfterSeconds": 30, "rejectOrCancelCollateralReservationReturnFactorBIPS": 9500, "rejectRedemptionRequestWindowSeconds": 120, "takeOverRedemptionRequestWindowSeconds": 120, "rejectedRedemptionDefaultFactorVaultCollateralBIPS": 10500, "rejectedRedemptionDefaultFactorPoolBIPS": 0, "transferFeeMillionths": 0, "transferFeeClaimFirstEpochStartTs": 1727784000, "transferFeeClaimEpochDurationSeconds": 604800, "transferFeeClaimMaxUnexpiredEpochs": 16, "coreVaultNativeAddress": "0xfeC5BF0a64963E6b2EbA8153235E40f9e5a8c8Ae", "coreVaultTransferFeeBIPS": 0, "coreVaultTransferTimeExtensionSeconds": 7200, "coreVaultMinimumAmountLeftBIPS": 2000, "coreVaultRedemptionFeeBIPS": 0, "coreVaultMinimumRedeemLots": 10}