{"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "definitions": {"CollateralTypeParameters": {"additionalProperties": false, "properties": {"assetFtsoSymbol": {"description": "FTSO symbol for the asset, relative to this token or a reference currency\n(it depends on the value of `directPricePair`).", "pattern": "^\\w+$", "type": "string"}, "ccbMinCollateralRatioBIPS": {"description": "Minimum collateral ratio for agent in CCB (Collateral call band).\nIf the agent's collateral ratio is less than this, skip the CCB and go straight to liquidation.\nA bit smaller than minCollateralRatioBIPS.", "minimum": 0, "type": "integer"}, "decimals": {"description": "Same as token.decimals(), when that exists.", "minimum": 0, "type": "integer"}, "directPricePair": {"description": "When `true`, the FTSO with symbol `assetFtsoSymbol` returns asset price relative to this token\n(such FTSO's will probably exist for major stablecoins).\nWhen `false`, the FTSOs with symbols `assetFtsoSymbol` and `tokenFtsoSymbol` give asset and token\nprice relative to the same reference currency and the asset/token price is calculated as their ratio.", "type": "boolean"}, "minCollateralRatioBIPS": {"description": "Minimum collateral ratio for healthy agents.", "minimum": 0, "type": "integer"}, "safetyMinCollateralRatioBIPS": {"description": "Minimum collateral ratio required to get agent out of liquidation.\nWill always be greater than minCollateralRatioBIPS.", "minimum": 0, "type": "integer"}, "token": {"description": "The ERC20 token contract for this collateral type.\nCan be an address (0x...) or a name of a contract in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "tokenFtsoSymbol": {"description": "FTSO symbol for this token in reference currency.\nUsed for asset/token price calculation when `directPricePair` is `false`.\nOtherwise it is irrelevant to asset/token price calculation, but is still used\nin calculation of challenger rewards, confirmation rewards and token burning.", "pattern": "^\\w+$", "type": "string"}}, "required": ["assetFtsoSymbol", "ccbMinCollateralRatioBIPS", "decimals", "directPricePair", "minCollateralRatioBIPS", "safetyMinCollateralRatioBIPS", "token", "tokenFtsoSymbol"], "type": "object"}}, "properties": {"$schema": {"description": "JSON schema url", "type": "string"}, "agentExitAvailableTimelockSeconds": {"description": "Amount of seconds that have to pass between available list exit announcement and execution.", "minimum": 0, "type": "integer"}, "agentFeeChangeTimelockSeconds": {"description": "Amount of seconds that have to pass between agent fee and pool fee share change announcement and execution.", "minimum": 0, "type": "integer"}, "agentMintingCRChangeTimelockSeconds": {"description": "Amount of seconds that have to pass between agent-set minting collateral ratio (vault or pool)\nchange announcement and execution.", "minimum": 0, "type": "integer"}, "agentOwnerRegistry": {"description": "The agent whitelist contains a list of allowed agent owners.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'AgentOwnerRegistry' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "agentTimelockedOperationWindowSeconds": {"description": "Amount of seconds that an agent is allowed to execute an update once it is allowed.", "minimum": 60, "type": "integer"}, "agentVaultFactory": {"description": "The factory contract for creating agent vaults.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'AgentVaultFactory' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "announcedUnderlyingConfirmationMinSeconds": {"description": "Minimum time that has to pass between underlying withdrawal announcement and the confirmation.\nAny value is ok, but higher values give more security against multiple announcement attack by a miner.\nShouldn't be much bigger than Flare data connector response time, so that payments can be confirmed without\nextra wait. Should be smaller than confirmationByOthersAfterSeconds (e.g. less than 1 hour).", "minimum": 0, "type": "integer"}, "assetDecimals": {"description": "The number of decimals in asset.", "minimum": 0, "type": "integer"}, "assetManagerController": {"description": "The asset manager controller which manages all the asset manager settings.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'AssetManagerController' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "assetMintingDecimals": {"description": "The number of decimals of precision for minting.\nUsually it is the same as assetDecimals (e.g. 8 for BTC).\nBut for some asset types e.g. ethereum, the number of asset decimals is 18, so we internally\nmanage all mintings with a smaller number of decimals (e.g. 9).\nThe maximum number of decimals must be such that the total supply of fasset will never\nexceed 64bit when expressed as fixed point with this many decimals (this allows for storage optimization).", "minimum": 0, "type": "integer"}, "assetName": {"description": "Name of the original asset on the underlying chain.", "type": "string"}, "assetSymbol": {"description": "Symbol for the original asset on the underlying chain.", "pattern": "^\\w+$", "type": "string"}, "attestationWindowSeconds": {"description": "Maximum time for which it is possible to obtain payment or non-payment proofs.", "minimum": 0, "type": "integer"}, "averageBlockTimeMS": {"description": "Average time between two successive blocks on the underlying chain, in milliseconds.", "minimum": 0, "type": "integer"}, "burnAddress": {"description": "Address for burning native currency (e.g. for collateral reservation fee after successful minting).", "pattern": "^0x[0-9a-fA-F]{40}$", "type": "string"}, "buybackCollateralFactorBIPS": {"description": "Ratio at which the agents can buy back their collateral when f-asset is terminated.\nTypically a bit more than 1 to incentivize agents to buy f-assets and self-close instead.", "minimum": 0, "type": "integer"}, "cancelCollateralReservationAfterSeconds": {"description": "The amount of time after which the collateral reservation can be canceled if the\nhandshake is not completed.", "minimum": 1, "type": "integer"}, "ccbTimeSeconds": {"description": "Agent can remain in CCB for this much time, after that liquidation starts automatically.", "minimum": 0, "type": "integer"}, "chainName": {"description": "Chain name; must match the Flare data connector chainId, when encoded as bytes.", "minimum": 0, "type": "string"}, "collateralPoolFactory": {"description": "The factory contract for creating agent collateral pools.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'CollateralPoolFactory' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "collateralPoolTokenFactory": {"description": "The factory contract for creating agent collateral pools.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'CollateralPoolTokenFactory' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "collateralPoolTokenTimelockSeconds": {"description": "Amount of seconds that a collateral pool enterer must wait before spending obtained tokens.", "type": "integer"}, "collateralReservationFeeBIPS": {"description": "Collateral reservation fee that must be paid by the minter.\nPayment is in NAT, but is proportional to the value of assets to be minted.", "minimum": 0, "type": "integer"}, "confirmationByOthersAfterSeconds": {"description": "If the agent or redeemer becomes unresponsive, we still need payment or non-payment confirmations\nto be presented eventually to properly track agent's underlying balance.\nTherefore we allow anybody to confirm payments/non-payments this many seconds after request was made.", "minimum": 0, "type": "integer"}, "confirmationByOthersRewardUSD5": {"description": "The user who makes abandoned redemption confirmations gets rewarded by the following amount.\nThe payment is in agent's vault collateral tokens, converted from the usd amount in this setting.", "pattern": "^[0-9 ]+$", "type": "string"}, "coreVaultMinimumAmountLeftBIPS": {"description": "The minimum amount that has to be left on agent's underlying address after the transfer to the core vault,\nto make sure that the agent stays redeemable.\n\nExpressed as a percentage of the \"agent's minting capacity\", which is the amount of backed assets at which\nthe agent's vault or pool collateral ratio falls to the corresponding system minimum collateral ratio.\n\nWarning: agent minting capacity is based on the system min CR, which is typicaly 10-20% (or more) smaller\nthan the agent's minting CR. We intentioanaly use minCR instead of mintingCR, because that would be gameable.\nHowever, this means that to make a typical agent keep about 50% of their minting capacity,\nthis value should be set 20% lower, e.g. to 40%.", "type": "integer"}, "coreVaultMinimumRedeemLots": {"description": "The minimum amount of lots that can be redeemed directly from the core vault, to lower the amount of transactions that\nthe core vault has to perform.", "type": "integer"}, "coreVaultNativeAddress": {"description": "The address that collects the core vault transfer fee.", "type": "string"}, "coreVaultRedemptionFeeBIPS": {"description": "The fee charged for direct redemption from the core vault.", "type": "integer"}, "coreVaultTransferFeeBIPS": {"description": "The fee that the agent has to pay for transfer to core vault.\nThe fee amount is proportional to the transfer amount and paid in FLR/SGB.", "type": "integer"}, "coreVaultTransferTimeExtensionSeconds": {"description": "Extra time that transfer to core vault redemption payments get compared\nto ordinary redemptions.", "type": "integer"}, "diamondCutMinTimelockSeconds": {"description": "Minimum time that the system must wait before performing diamond cut.\nThe actual timelock is the maximum of this setting and GovernanceSettings.timelock.", "type": "integer"}, "emergencyPauseDurationResetAfterSeconds": {"description": "The amount of time since last emergency pause after which the total pause duration counter\nwill reset automatically.", "type": "integer"}, "fAssetName": {"description": "The name of the f-asset.", "type": "string"}, "fAssetSymbol": {"description": "The symbol for the f-asset.", "pattern": "^\\w+$", "type": "string"}, "fdcVerification": {"description": "The proof verifier contract for Flare data connector proofs.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'FdcVerification' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "liquidationCollateralFactorBIPS": {"description": "Factor with which to multiply the asset price in native currency to obtain the payment to the liquidator.\nExpressed in BIPS, e.g. [12000, 16000, 20000] means that the liquidator will be paid 1.2, 1.6 and 2.0\ntimes the market price of the liquidated assets after each `liquidationStepSeconds`.\nValues in the array must increase and be greater than 100%.", "items": {"type": "number"}, "type": "array"}, "liquidationFactorVaultCollateralBIPS": {"description": "How much of the liquidation is paid in vault collateral.\nExpressed in BIPS relative to the liquidated FAsset value at current price.\nThe remainder will be paid in pool NAT collateral.", "items": {"type": "number"}, "type": "array"}, "liquidationStepSeconds": {"description": "If there was no liquidator for the current liquidation offer,\ngo to the next step of liquidation after a certain period of time.", "minimum": 1, "type": "integer"}, "lotSize": {"description": "Lot size in base unit of underlying asset (e.g. satoshi or wei).\nMay change, which affects subsequent mintings and redemptions.", "pattern": "^[0-9 ]+$", "type": "string"}, "maxEmergencyPauseDurationSeconds": {"description": "The maximum total pause that can be triggered by non-governance (but governance allowed) caller.\nThe duration count can be reset by the governance.", "type": "integer"}, "maxRedeemedTickets": {"description": "To prevent unbounded work, the number of tickets redeemed in a single request is limited.", "minimum": 1, "type": "integer"}, "maxTrustedPriceAgeSeconds": {"description": "Maximum age that trusted price feed is valid.\nOtherwise (if there were no trusted votes for that long) just use generic ftso price feed.", "minimum": 0, "type": "integer"}, "minUnderlyingBackingBIPS": {"description": "The percentage of minted f-assets that the agent must hold in his underlying address.", "maximum": 10000, "minimum": 0, "type": "integer"}, "minUpdateRepeatTimeSeconds": {"description": "Minimum time after an update of a setting before the same setting can be updated again.", "minimum": 0, "type": "integer"}, "mintingCap": {"description": "Maximum minted amount of the f-asset, in base unit of underlying asset.", "pattern": "^[0-9 ]+$", "type": "string"}, "mintingPoolHoldingsRequiredBIPS": {"description": "The minimum amount of pool tokens the agent must hold to be able to mint.\nTo be able to mint, the NAT value of all backed fassets together with new ones times this percentage\nmust be smaller than the agent's pool tokens' amount converted to NAT.", "minimum": 0, "type": "integer"}, "paymentChallengeRewardBIPS": {"description": "Challenge reward can be composed of two part - fixed and proportional (any of them can be zero).\nThe payment is in agent's vault collateral tokens. This is the proportional part (in BIPS).", "minimum": 0, "type": "integer"}, "paymentChallengeRewardUSD5": {"description": "Challenge reward can be composed of two part - fixed and proportional (any of them can be zero).\nThe payment is in agent's vault collateral tokens. This is the fixed part, converted from\nthe usd amount in this setting.", "pattern": "^[0-9 ]+$", "type": "string"}, "poolCollateral": {"$ref": "#/definitions/CollateralTypeParameters", "description": "Data about the collateral used in the collateral pool, token is always WNat (FLR/SGB)."}, "poolExitAndTopupChangeTimelockSeconds": {"description": "Amount of seconds that have to pass between agent-set settings for pool exit and topup\n(exit CR, topup CR, topup bonus) change announcement and execution.", "minimum": 0, "type": "integer"}, "poolTokenSuffix": {"description": "The suffix to pool token name and symbol that identifies new vault's collateral pool token.\nWhen vault is created, the owner passes own suffix which will be appended to this.", "type": "string"}, "priceReader": {"description": "Price reader contract is a simple abstraction of FTSO system.\nCan be a contract address (0x...) or a name in contracts.json.\nOptional, default is 'FdcVerification' in contracts.json.", "pattern": "^\\w+$", "type": "string"}, "redemptionDefaultFactorPoolBIPS": {"description": "On redemption underlying payment failure, redeemer is compensated with\nredemption value recalculated times redemption failure factor.\nThis is the part of factor paid from pool in FLR/SGB.", "minimum": 0, "type": "integer"}, "redemptionDefaultFactorVaultCollateralBIPS": {"description": "On redemption underlying payment failure, redeemer is compensated with\nredemption value recalculated times redemption failure factor.\nThis is the part of factor paid from agent's vault collateral.", "minimum": 0, "type": "integer"}, "redemptionFeeBIPS": {"description": "Redemption fee as percentage of the redemption amount.", "minimum": 0, "type": "integer"}, "redemptionPaymentExtensionSeconds": {"description": "When there are many redemption requests in short time, agent gets\nup to this amount of extra payment time per redemption.", "type": "integer"}, "rejectOrCancelCollateralReservationReturnFactorBIPS": {"description": "The amount of collateral reservation fee returned to the minter in case of rejection or cancellation.\nExpressed in BIPS, e.g. 9500 for factor of 0.95.", "maximum": 10000, "minimum": 0, "type": "integer"}, "rejectRedemptionRequestWindowSeconds": {"description": "Time window inside which the agent can reject the redemption request.", "minimum": 1, "type": "integer"}, "rejectedRedemptionDefaultFactorPoolBIPS": {"description": "This is the part of rejected redemption factor paid from agent's pool collateral.", "minimum": 0, "type": "integer"}, "rejectedRedemptionDefaultFactorVaultCollateralBIPS": {"description": "On redemption rejection, without take over, redeemer is compensated with\nredemption value recalculated in flare/sgb times redemption failure factor.\nExpressed in BIPS, e.g. 12000 for factor of 1.2.\nThis is the part of factor paid from agent's vault collateral.", "minimum": 0, "type": "integer"}, "requireEOAAddressProof": {"description": "For some chains (e.g. Ethereum) we require that agent proves that underlying address is an EOA address.\nThis must be done by presenting a payment proof from that address.", "type": "boolean"}, "takeOverRedemptionRequestWindowSeconds": {"description": "Time window inside which the agent can take over the redemption request from another agent\nthat has rejected it.", "minimum": 1, "type": "integer"}, "tokenInvalidationTimeMinSeconds": {"description": "Minimum time from the moment token is deprecated to when it becomes invalid and agents still using\nit as vault collateral get liquidated.", "minimum": 0, "type": "integer"}, "transferFeeClaimEpochDurationSeconds": {"description": "Transfer fees are collected for each \"epoch\" and can be claimed after the epoch ends.\nThis setting is the epoch duration (in seconds).", "type": "integer"}, "transferFeeClaimFirstEpochStartTs": {"description": "Transfer fees are collected for each \"epoch\" and can be claimed after the epoch ends.\nThis setting marks the start timestamp of the epoch 0.", "type": "integer"}, "transferFeeClaimMaxUnexpiredEpochs": {"description": "After a while, the epochs become unclaimable and the fees in there are transferred to the latest epoch.\nThis setting is the number of epochs before expiration.", "type": "integer"}, "transferFeeMillionths": {"description": "The fee paid for FAsset transfers.\nUnlike other ratios that are in BIPS, this one is in millionths (1/1000000), which is 1/100 of a BIP.\nThis is because the values can be very small, just a few BIPS.", "type": "integer"}, "underlyingBlocksForPayment": {"description": "Number of underlying blocks that the minter or agent is allowed to pay underlying value.\nIf payment not reported in that time, minting/redemption can be challenged and default action triggered.\nCAREFUL: Count starts from the current proved block height, so the minters and agents should\nmake sure that current block height is fresh, otherwise they might not have enough time for payment.", "minimum": 0, "type": "integer"}, "underlyingSecondsForPayment": {"description": "Minimum time to allow agent to pay for redemption or minter to pay for minting.\nThis is useful for fast chains, when there can be more than one block per second.\nRedemption/minting payment failure can be called only after underlyingSecondsForPayment have elapsed\non underlying chain.\nCAREFUL: Count starts from the current proved block timestamp, so the minters and agents should\nmake sure that current block timestamp is fresh, otherwise they might not have enough time for payment.\nThis is partially mitigated by adding local duration since the last block height update to\nthe current underlying block timestamp.", "minimum": 0, "type": "integer"}, "userWhitelist": {"description": "If non-null, the whitelist contains a list of accounts that can call public methods\n(minting, redeeming, challenging, etc.)\nIf null, there will be no user whitelisting.\nCan be a contract address (0x...) or a name in contracts.json.", "pattern": "^\\w+$", "type": ["null", "string"]}, "vaultCollateralBuyForFlareFactorBIPS": {"description": "On some rare occasions (stuck minting, locked fassets after termination), the agent has to unlock\ncollateral. For this, part of collateral corresponding to FTSO asset value is burned and the rest is released.\nHowever, we cannot burn typical vault collateral (stablecoins), so the agent must buy them for NAT\nat FTSO price multiplied with this factor (should be a bit above 1) and then we burn the NATs.", "minimum": 0, "type": "integer"}, "vaultCollaterals": {"description": "The data about allowed vault collateral types.", "items": {"$ref": "#/definitions/CollateralTypeParameters"}, "type": "array"}, "withdrawalWaitMinSeconds": {"description": "Agent has to announce any collateral withdrawal ar vault destroy and then wait for at least\nwithdrawal<PERSON>ait<PERSON>inSeconds. This prevents challenged agent to remove all collateral before\nchallenge can be proved.", "minimum": 0, "type": "integer"}}, "required": ["agentExitAvailableTimelockSeconds", "agentFeeChangeTimelockSeconds", "agentMintingCRChangeTimelockSeconds", "agentTimelockedOperationWindowSeconds", "announcedUnderlyingConfirmationMinSeconds", "assetDecimals", "assetMintingDecimals", "assetName", "assetSymbol", "attestationWindowSeconds", "averageBlockTimeMS", "<PERSON><PERSON><PERSON><PERSON>", "buybackCollateralFactorBIPS", "cancelCollateralReservationAfterSeconds", "ccbTimeSeconds", "chainName", "collateralPoolTokenTimelockSeconds", "collateralReservationFeeBIPS", "confirmationByOthersAfterSeconds", "confirmationByOthersRewardUSD5", "coreVaultMinimumAmountLeftBIPS", "coreVaultMinimumRedeemLots", "coreVaultNativeAddress", "coreVaultRedemptionFeeBIPS", "coreVaultTransferFeeBIPS", "coreVaultTransferTimeExtensionSeconds", "diamondCutMinTimelockSeconds", "emergencyPauseDurationResetAfterSeconds", "fAssetName", "fAssetSymbol", "liquidationCollateralFactorBIPS", "liquidationFactorVaultCollateralBIPS", "liquidationStepSeconds", "lotSize", "maxEmergencyPauseDurationSeconds", "maxRedeemedTickets", "maxTrustedPriceAgeSeconds", "minUnderlyingBackingBIPS", "minUpdateRepeatTimeSeconds", "mintingCap", "mintingPoolHoldingsRequiredBIPS", "paymentChallengeRewardBIPS", "paymentChallengeRewardUSD5", "poolCollateral", "poolExitAndTopupChangeTimelockSeconds", "poolTokenSuffix", "redemptionDefaultFactorPoolBIPS", "redemptionDefaultFactorVaultCollateralBIPS", "redemptionFeeBIPS", "redemptionPaymentExtensionSeconds", "rejectOrCancelCollateralReservationReturnFactorBIPS", "rejectRedemptionRequestWindowSeconds", "rejectedRedemptionDefaultFactorPoolBIPS", "rejectedRedemptionDefaultFactorVaultCollateralBIPS", "requireEOAAddressProof", "takeOverRedemptionRequestWindowSeconds", "tokenInvalidationTimeMinSeconds", "transferFeeClaimEpochDurationSeconds", "transferFeeClaimFirstEpochStartTs", "transferFeeClaimMaxUnexpiredEpochs", "transferFeeMillionths", "underlyingBlocksForPayment", "underlyingSecondsForPayment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vaultCollateralBuyForFlareFactorBIPS", "vaultCollaterals", "withdrawalWaitMinSeconds"], "type": "object"}