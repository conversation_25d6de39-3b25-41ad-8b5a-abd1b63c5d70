{"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"$schema": {"description": "JSON schema url", "type": "string"}, "assetManager": {"description": "The corrsponding asset manager, either the address or the name in the contracts file.", "type": "string"}, "chainPaymentFee": {"description": "The fee charged by the chain for each payment, in chain base units.", "pattern": "^[0-9 ]+$", "type": "string"}, "custodianAddress": {"description": "The underlying address of the core vault custodian.", "pattern": "^\\w+$", "type": "string"}, "escrowAmount": {"description": "Single escrow amount, in chain base units (setting to 0 disables escrows).", "pattern": "^[0-9 ]+$", "type": "string"}, "escrowEndTimeSeconds": {"description": "The time of day (UTC) when the escrows expire. Exactly one escrow per day will expire.", "type": "integer"}, "initialSequenceNumber": {"description": "The nonce (sequence number) on the multisig address at the deploy time.\nAfter deploy, only transactions requested by the core vault manager should be sent from this address, otherwise the nsequence numbering will lose sync.", "pattern": "^\\w+$", "type": "integer"}, "minimalAmountLeft": {"description": "The minimal amount that will be left on the multisig after escrowing, in chain base units.", "pattern": "^[0-9 ]+$", "type": "string"}, "underlyingAddress": {"description": "The underlying address of the core vault multisig.", "pattern": "^\\w+$", "type": "string"}}, "required": ["assetManager", "chainPaymentFee", "custodian<PERSON><PERSON><PERSON>", "escrowAmount", "escrowEndTimeSeconds", "initialSequenceNumber", "minimalAmountLeft", "underlyingAddress"], "type": "object"}