{"contractName": "FtsoV2PriceStoreMock", "firstVotingRoundStartTs": **********, "votingEpochDurationSeconds": 90, "trustedProviders": ["******************************************"], "trustedProvidersThreshold": 1, "maxSpreadBIPS": 50, "feeds": [{"feedId": "SGB/USD", "symbol": "CFLR", "feedDecimals": 7}, {"feedId": "BTC/USD", "symbol": "testBTC", "feedDecimals": 2}, {"feedId": "XRP/USD", "symbol": "testXRP", "feedDecimals": 5}, {"feedId": "DOGE/USD", "symbol": "testDOGE", "feedDecimals": 5}, {"feedId": "ETH/USD", "symbol": "testETH", "feedDecimals": 3}, {"feedId": "USDC/USD", "symbol": "testUSDC", "feedDecimals": 5}, {"feedId": "USDT/USD", "symbol": "testUSDT", "feedDecimals": 5}]}