{"extends": "solhint:recommended", "plugins": [], "rules": {"avoid-call-value": "error", "avoid-low-level-calls": "error", "avoid-sha3": "error", "avoid-suicide": "error", "avoid-throw": "error", "avoid-tx-origin": "warn", "check-send-result": "error", "compiler-version": ["off", "0.8.23"], "const-name-snakecase": "error", "func-name-mixedcase": "error", "gas-custom-errors": "off", "ordering": "off", "imports-on-top": "error", "immutable-vars-naming": "off", "max-line-length": ["error", 119], "no-global-import": "off", "reason-string": "off", "max-states-count": ["error", 20], "multiple-sends": "error", "no-complex-fallback": "error", "no-empty-blocks": "off", "no-inline-assembly": "error", "no-unused-vars": "error", "not-rely-on-block-hash": "error", "not-rely-on-time": "off", "payable-fallback": "error", "quotes": ["error", "double"], "reentrancy": "error", "state-visibility": "error", "use-forbidden-name": "error", "var-name-mixedcase": "error", "visibility-modifier-order": "error", "func-visibility": ["warn", {"ignoreConstructors": true}]}}