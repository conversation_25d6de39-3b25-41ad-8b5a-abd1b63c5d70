# This configuration file was automatically generated by Gitpod.
# Please adjust to your needs (see https://www.gitpod.io/docs/introduction/learn-gitpod/gitpod-yaml)
# and commit this file to your remote git repository to share the goodness with others.

# Learn more from ready-to-use templates: https://www.gitpod.io/docs/introduction/getting-started/quickstart

tasks:

  - name: Install node modules
    init: |
      yarn install --frozen-lockfile
      gp sync-done install_node_modules
      exit

  - name: Compile contracts
    before: gp sync-await install_node_modules
    command: |
      yarn run compile
      gp sync-done compile_contracts
      exit

  - name: "Test: linter"
    before: gp sync-await install_node_modules
    command: yarn run lint && exit
