workflow:
  rules:
    - if: '$CI_OPEN_MERGE_REQUESTS != null && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "webide")'
      when: never
    - when: always

stages:
- build
- test

include:
- template: Jobs/SAST.latest.gitlab-ci.yml

sast:
  needs: []

default:
  tags:
  - flarenetwork



.test-node-sc:
  stage: test
  image: node:18
  timeout: 3h
  needs:
  - build-smart-contracts
  cache:
    policy: pull
    when: always
    key:
      files:
      - yarn.lock
    paths:
    - node_modules
  variables:
    DEPLOYER_PRIVATE_KEY: "0xc5e8f61d1ab959b397eecc0a37a6517b8e67a0e7cf1f4bce5591f3ed80199122"
    GENESIS_GOVERNANCE_PRIVATE_KEY: "0x50777f5a3ce16445e63411bf1e865a2a11d5ca3c4cbc1de00808a52180bd8d3c"
    GOVERNANCE_PRIVATE_KEY: "0xd49743deccbccc5dc7baa8e69e5be03298da8688a15dd202e20f15d5e0e9a9fb"
    GOVERNANCE_PUBLIC_KEY: "0xeAD9C93b79Ae7C1591b1FB5323BD777E86e150d4"
    GOVERNANCE_EXECUTOR_PUBLIC_KEY: "0xE5904695748fe4A84b40b3fc79De2277660BD1D3"
  dependencies:
  - build-smart-contracts
  before_script:
  - yarn install --frozen-lockfile


.test-node-sc-noschedule:
  extends: .test-node-sc
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    - when: on_success


### Jobs


build-smart-contracts:
  stage: build
  needs: []
  image: node:18
  cache:
    policy: pull-push
    when: on_success
    key:
      files:
      - yarn.lock
    paths:
      - node_modules
  before_script:
  - yarn install --frozen-lockfile
  script:
  - yarn compile
  artifacts:
    paths:
    - artifacts
    - cache
    - typechain-truffle
    expire_in: 1 week


test-unit:
  extends: .test-node-sc-noschedule
  tags:
  - flarenetwork-md
  parallel:
    matrix:
    - TEST_PATH:
      - ./test/unit/fasset/!(implementation)
      - ./test/unit/!(fasset)
  script:
  - yarn hardhat test --network hardhat

test-unit-lg:
  extends: test-unit
  tags:
  - flarenetwork-lg
  parallel:
    matrix:
    - TEST_PATH:
      - ./test/unit/fasset/implementation


test-contract-integration:
  extends: .test-node-sc-noschedule
  tags:
  - flarenetwork-md
  script:
  - yarn test_integration_hh


test-linter:
  extends: .test-node-sc-noschedule
  script:
  - yarn lint


test-slither-check:
  extends: .test-node-sc-noschedule
  variables:
    PIP_BREAK_SYSTEM_PACKAGES: "1"
  before_script:
  - apt update
  - apt install -y python3-pip
  - pip3 install slither-analyzer
  - yarn install --frozen-lockfile
  script:
  - yarn slither


coverage:schedule:
  extends: .test-node-sc
  stage: test
  tags:
  - flarenetwork-lg
  retry: 2
  script:
  - yarn cov
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    when: always
    expire_in: 1 month
    paths:
      - ./coverage/
      - ./coverage.json
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
  rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_NAME == "coverage"'
    when: on_success
  - when: never

fuzzer:schedule:
  extends: .test-node-sc
  stage: test
  tags:
  - flarenetwork-lg
  timeout: 4 hours
  parallel: 3
  variables:
    NODE_OPTIONS: "--max_old_space_size=6144"
    LOOPS: 5000
    CHANGE_PRICE_AT: '300,600,...'
    # CHANGE_PRICE_FACTOR: '{"default": [0.9, 1.1]}'
  script:
  - yarn fuzzing_fasset
  artifacts:
    paths:
    - test_logs/fasset-fuzzing.log
    name: fasset-fuzzing
    when: always
    expire_in: 30 days
  rules:
  - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_NAME == "fuzzer"'
    when: on_success
  - when: never
