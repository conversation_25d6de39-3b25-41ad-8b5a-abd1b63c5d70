{"name": "fasset", "version": "1.1.0", "description": "Smart contracts implementing FAsset system.", "main": "", "repository": {"type": "git", "url": "git+https://gitlab.com/flarenetwork/fasset.git"}, "author": "Flare Labs", "license": "MIT", "directories": {}, "engines": {"node": ">=16"}, "scripts": {"---------TEST---SCRIPTS": "", "test": "yarn hardhat test", "coverage": "env NODE_OPTIONS=\"--max_old_space_size=8192\" yarn hardhat coverage --testfiles", "test-with-coverage": "yarn clean && yarn compile && yarn coverage \"test/unit test/integration\"", "cov": "yarn test-with-coverage", "testHH": "yarn hardhat test \"test/{unit,integration}/**/*.ts\"", "test_unit_hh": "env TEST_PATH=./test/unit yarn hardhat test", "test_integration_hh": "env TEST_PATH=./test/integration yarn hardhat test", "fuzzing_fasset": "yarn test test/fuzzing/fasset/FAssetFuzzing.ts", "tsrun": "yarn ts-node --files=./type-extensions.ts", "---------COMPILE---SCRIPTS": "", "clean": "env rm -rf cache artifacts build build-info typechain typechain-truffle", "clean-all": "yarn clean && env rm -rf node_modules", "compile": "yarn hardhat compile && yarn typechain-prepare && yarn typechain-truffle-v5 && yarn typechain-after", "c": "yarn compile && yarn lint", "lint": "yarn solhint \"contracts/**/*.sol\"", "typechain-prepare": "env bash scripts/remove-flattened-duplicates.sh", "typechain-truffle-v5": "yarn typechain --target=truffle-v5 --out-dir typechain-truffle \"artifacts/!(build-info|flattened)/**/+([a-zA-Z0-9_]).json\"", "typechain-after": "env rm -rf artifacts/flare-sc", "size": "yarn run hardhat size-contracts", "flatten": "yarn hardhat flatten", "install-slither": "which slither > /dev/null || PIP_BREAK_SYSTEM_PACKAGES=1 pip3 install slither-analyzer", "slither": "yarn install-slither; rm -f ./slither.json 2> /dev/null; slither . --json=./slither.json 2> /dev/null || true; node node_modules/flare-smart-contracts/scripts/slither-parse.js ./slither.json", "slither-show-stderr": "yarn install-slither; rm -f ./slither.json 2> /dev/null; slither . --json=./slither.json || true; node node_modules/flare-smart-contracts/scripts/slither-parse.js ./slither.json", "flatten-flare-sc": "yarn flatten-dependencies node_modules/flare-smart-contracts scripts/flatten-files.txt flattened/FlareSmartContracts.sol scripts/flatten-extra-imports.txt", "flatten-dependencies": "env bash scripts/flatten-dependencies.sh", "ts-compile-watch": "tsc --watch --noEmit", "eslint": "eslint \"lib/**/*.ts\" \"test/**/*.ts\"", "generate-json-schema": "typescript-json-schema --noExtraProps --required --strictNullChecks", "generate-parameter-schema": "yarn generate-json-schema deployment/lib/asset-manager-parameters.ts AssetManagerParameters -o deployment/config/asset-manager-parameters.schema.json", "---------DEPLOY---SCRIPTS---HARDHAT": "", "local": "yarn hardhat --network local", "full-deploy-hardhat": "yarn local deploy-price-reader-v2 && yarn local deploy-asset-manager-dependencies && yarn local deploy-asset-managers --deploy-controller --all", "full-deploy-hardhat-test": "yarn local test --no-compile deployment/test/test-deployed-contracts.ts", "mock-deploy-hardhat": "rm -f deployment/deploys/hardhat.json && yarn local run deployment/test/scripts/mock-deploy-dependencies.ts && yarn local run deployment/test/scripts/mock-deploy-stablecoins.ts && yarn full-deploy-hardhat && yarn full-deploy-hardhat-test", "flare-sc-deploy-hardhat": "yarn local run deployment/test/scripts/mock-deploy-stablecoins.ts && yarn full-deploy-hardhat && yarn full-deploy-hardhat-test", "---------DEPLOY---SCRIPTS---COSTON": "", "coston": "yarn hardhat --network coston", "deploy-mock-stablecoins-coston": "yarn coston run deployment/test/scripts/mock-deploy-stablecoins.ts", "deploy-price-reader-v2-coston": "yarn coston deploy-price-reader-v2", "deploy-dependencies-coston": "yarn coston deploy-asset-manager-dependencies", "deploy-with-controller-coston": "yarn coston deploy-asset-managers --deploy-controller --all", "full-deploy-coston-test": "yarn coston test --no-compile deployment/test/test-deployed-contracts.ts", "verify-coston": "yarn coston verify-contract", "verify-asset-manager-coston": "yarn coston verify-asset-managers --all", "verify-asset-manager-controller-coston": "yarn coston verify-asset-manager-controller", "verify-asset-manager-facets-coston": "yarn coston verify-asset-manager-facets", "console": "yarn hardhat console --no-compile --network", "console-coston": "yarn hardhat console --no-compile --network coston", "---------DEPLOY---SCRIPTS---SONGBIRD": "", "songbird": "yarn hardhat --network songbird", "verify-songbird": "yarn songbird verify-contract", "console-songbird": "yarn hardhat console --no-compile --network songbird", "---------INFO---SCRIPTS": "", "gas-snapshot": "env CI=true yarn testHH; yarn gas-report", "gas-report": "ts-node node_modules/flare-smart-contracts/scripts/process-gas-report.ts && cat .gas-report.txt", "gas-report-check": "node_modules/flare-smart-contracts/scripts/gas-report-check.sh", "gas": "cat .gas-report.txt"}, "dependencies": {"@openzeppelin/contracts": "4.9.2", "flare-smart-contracts": "git+https://gitlab.com/flarenetwork/flare-smart-contracts.git#flare-distribution-to-delegators", "flare-smart-contracts-v2": "git+https://github.com/flare-foundation/flare-smart-contracts-v2.git", "@flarenetwork/state-connector-protocol": "1.0.0-beta.6"}, "devDependencies": {"@gnosis.pm/mock-contract": "4.0.0", "@nomicfoundation/hardhat-network-helpers": "1.0.11", "@nomicfoundation/hardhat-verify": "2.0.8", "@nomiclabs/hardhat-truffle5": "2.0.7", "@nomiclabs/hardhat-web3": "2.0.0", "@openzeppelin/test-helpers": "0.5.16", "@swyftx/api-crypto-address-validator": "1.13.1", "@typechain/hardhat": "8.0.2", "@typechain/truffle-v5": "7.0.0", "@typechain/web3-v1": "6.0.5", "@types/chai": "4.3.5", "@types/mocha": "10.0.1", "@types/node": "16.18.39", "@typescript-eslint/eslint-plugin": "6.1.0", "@typescript-eslint/parser": "6.1.0", "chai": "4.3.7", "dotenv": "16.3.1", "eslint": "8.45.0", "eth-sig-util": "3.0.1", "ethereumjs-util": "7.1.5", "glob": "9.3.0", "hardhat": "2.22.5", "hardhat-contract-sizer": "2.10.0", "hardhat-gas-reporter": "1.0.9", "intercept-stdout": "0.1.2", "solhint": "5.0.1", "solidity-coverage": "0.8.12", "ts-node": "10.9.2", "typechain": "8.3.2", "typescript": "5.1.6", "typescript-json-schema": "0.59.0", "yarn-audit-fix": "9.3.12"}, "packageManager": "yarn@1.22.22"}