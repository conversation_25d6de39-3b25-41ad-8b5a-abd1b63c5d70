{"files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "solidity.compileUsingRemoteVersion": "v0.8.20+commit.a1b79de6", "solidity.packageDefaultDependenciesDirectory": "node_modules", "yaml.customTags": ["!reference sequence"], "slither.solcPath": "", "slither.hiddenDetectors": [], "yaml.schemas": {"https://gitpod.io/schemas/gitpod-schema.json": "file:///workspace/fasset/.gitpod.yml"}, "wake.compiler.solc.remappings": ["@ensdomains/=node_modules/@ensdomains/", "@flarenetwork/=node_modules/@flarenetwork/", "@gnosis.pm/=node_modules/@gnosis.pm/", "@openzeppelin/=node_modules/@openzeppelin/", "@solidity-parser/=node_modules/flare-smart-contracts/node_modules/solhint/node_modules/@solidity-parser/", "eth-gas-reporter/=node_modules/eth-gas-reporter/", "flare-smart-contracts-v2/=node_modules/flare-smart-contracts-v2/", "flare-smart-contracts/=node_modules/flare-smart-contracts/", "forge-std/=node_modules/flare-smart-contracts-v2/lib/forge-std/src/", "hardhat/=node_modules/hardhat/"], "commentTranslate.targetLanguage": "ru"}