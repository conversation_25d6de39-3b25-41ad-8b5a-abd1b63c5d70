{"compilerOptions": {"target": "es2018", "module": "commonjs", "strict": true, "esModuleInterop": true, "declaration": true, "declarationMap": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true, "newLine": "lf", "outDir": "build", "typeRoots": ["test/types", "node_modules/@types"], "lib": ["ES2018"]}, "include": ["./lib", "./test", "deployment/lib", "deployment/test", "deployment/cuts", "./typechain-truffle"], "files": ["hardhat.config.ts", "hardhat-tasks.config.ts", "./type-extensions.ts"]}